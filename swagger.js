import swaggerAutogen from "swagger-autogen";

const doc = {
    info: {
        title: "Sistema PDV - Supermercado API",
        description: "API do sistema de Ponto de Venda para supermercado com gestão de produtos e operações de caixa",
        version: "1.0.0"
    },
    host: 'localhost:5000',
    basePath: '/',
    schemes: ['http'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
        {
            name: 'Autenticação',
            description: 'Endpoints para autenticação de usuários'
        },
        {
            name: 'Produtos',
            description: 'Gestão de produtos (apenas administradores)'
        },
        {
            name: 'Operações de Caixa',
            description: 'Operações de caixa para registro de compras'
        }
    ],
    components: {
        securitySchemes: {
            bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
                description: 'Token JWT obtido através do endpoint de login'
            }
        }
    },
    definitions: {
        LoginRequest: {
            type: 'object',
            properties: {
                email: {
                    type: 'string',
                    example: '<EMAIL>'
                },
                senha: {
                    type: 'string',
                    example: '12345'
                }
            },
            required: ['email', 'senha']
        },
        LoginResponse: {
            type: 'object',
            properties: {
                token: {
                    type: 'string',
                    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                }
            }
        },
        ProdutoRequest: {
            type: 'object',
            properties: {
                prd_nome: {
                    type: 'string',
                    example: 'Coca-Cola 2L'
                },
                prd_valor: {
                    type: 'number',
                    example: 5.99
                },
                prd_codigobarras: {
                    type: 'string',
                    example: '7894900011517'
                },
                cat_id: {
                    type: 'integer',
                    example: 4
                }
            },
            required: ['prd_nome', 'prd_valor', 'prd_codigobarras']
        }
    }
}

const outputJson = "./swagger-output.json";
const routes = ['./server.js']

swaggerAutogen({openapi: '3.0.0'})(outputJson, routes, doc)
.then( async () => {
    await import('./server.js');
})