{"openapi": "3.0.0", "info": {"title": "Sistema de Gerenciamento de RH - API", "description": "API completa para gerenciamento do departamento de Recursos Humanos, incluindo cadastro de funcionários, aplicação de aumentos salariais e geração de folhas de pagamento.", "version": "1.0.0", "contact": {"name": "DevMinds Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:5000/"}], "tags": [{"name": "Autenticação", "description": "Endpoints para autenticação de usuários"}, {"name": "Funcionários", "description": "Gerenciamento de funcionários"}, {"name": "Aumento Salarial", "description": "Aplicação e histórico de aumentos salariais"}, {"name": "Cargos", "description": "Gerenciamento de cargos"}, {"name": "Folha <PERSON>", "description": "Geração e consulta de folhas de pagamento"}], "paths": {"/auth/login": {"post": {"tags": ["Autenticação"], "summary": "Autenticar usuá<PERSON>", "description": "Realiza a autenticação do usuário e retorna um token JWT para acesso aos endpoints protegidos.", "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"example": "any"}, "senha": {"example": "any"}}}}}}}}, "/funcionarios/": {"post": {"tags": ["Funcionários"], "summary": "Cadastrar funcionário", "description": "Cadastra um novo funcionário no sistema.", "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"cpf": {"example": "any"}, "nome": {"example": "any"}, "salario": {"example": "any"}, "dataAdmissao": {"example": "any"}, "cargoId": {"example": "any"}}}}}}}, "get": {"tags": ["Funcionários"], "summary": "Listar funcionários", "description": "Retorna a lista de todos os funcionários cadastrados no sistema.", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/funcionarios/{id}": {"get": {"tags": ["Funcionários"], "summary": "Buscar funcionário por ID", "description": "Retorna os dados de um funcionário específico pelo seu ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Funcionários"], "summary": "<PERSON><PERSON><PERSON>", "description": "Realiza a demissão de um funcionário, definindo a data de demissão.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/aumentoSalario/aplicar": {"post": {"tags": ["Aumento Salarial"], "summary": "Aplicar aumento salarial", "description": "Aplica um aumento salarial percentual a todos os funcionários ativos e registra no histórico.", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"percentual": {"example": "any"}}}}}}}}, "/aumentoSalario/historico": {"get": {"tags": ["Aumento Salarial"], "summary": "Listar histó<PERSON>", "description": "Retorna o histórico de todos os aumentos salariais aplicados.", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/cargos/": {"get": {"tags": ["Cargos"], "summary": "Listar cargos", "description": "Retorna a lista de todos os cargos disponíveis no sistema.", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/folhaPagamento/gerar": {"post": {"tags": ["Folha <PERSON>"], "summary": "<PERSON><PERSON><PERSON> folha de paga<PERSON>", "description": "Gera uma nova folha de pagamento para o mês e ano especificados, incluindo todos os funcionários ativos.", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"mes": {"example": "any"}, "ano": {"example": "any"}}}}}}}}, "/folhaPagamento/": {"get": {"tags": ["Folha <PERSON>"], "summary": "Listar folhas de pagamento", "description": "Retorna a lista de todas as folhas de pagamento geradas.", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Token JWT obtido através do endpoint de autenticação"}}}, "security": [{"bearerAuth": []}]}