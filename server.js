import express from 'express'
import swaggerUi from 'swagger-ui-express'
import cookieParser from 'cookie-parser'
import { createRequire } from "module";

// Importar rotas
import authRoute from './routes/authRoute.js';
import produtoRoute from './routes/produtoRoute.js';
import compraRoute from './routes/compraRoute.js';

const require = createRequire(import.meta.url);
const outputJson = require("./swagger-output.json");

const app = express();

// Middlewares
app.use(express.json())
app.use(cookieParser());

// Configurar rotas
app.use('/auth', authRoute);
app.use('/produtos', produtoRoute);
app.use('/compras', compraRoute);

// Documentação Swagger
app.use("/docs", swaggerUi.serve, swaggerUi.setup(outputJson))

// Rota de teste
app.get('/', (req, res) => {
    res.json({
        message: "Sistema de PDV - Supermercado",
        version: "1.0.0",
        docs: "/docs"
    });
});

app.listen(5000, function() {
    console.log("Servidor PDV em funcionamento na porta 5000!");
    console.log("Documentação disponível em: http://localhost:5000/docs");
});